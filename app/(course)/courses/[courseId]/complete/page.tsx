import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { db } from '@/lib/db'
import { getProgress } from '@/actions/get-progress'
import { getUserInfo } from '@/actions/get-user-info'
import { CourseCertificate } from '@/components/course-certificate'
import { CourseReviews } from '@/components/course-reviews'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Trophy, Star } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ShareButton } from './_components/share-button'

type Params = Promise<{
  courseId: string
}>

interface CourseCompletePageProps {
  params: Params
}

export default async function CourseCompletePage({ params }: CourseCompletePageProps) {
  const { userId } = await auth()
  const { courseId } = await params

  if (!userId) {
    return redirect('/')
  }

  // Get course details
  const course = await db.course.findUnique({
    where: { id: courseId },
    include: {
      chapters: { where: { isPublished: true } },
      purchases: { where: { userId } },
    },
  })

  if (!course) {
    return redirect('/')
  }

  // Check if user has access
  const hasAccess = course.isFree || course.purchases.length > 0

  if (!hasAccess) {
    return redirect(`/courses/${courseId}`)
  }

  // Check completion status
  const progress = await getProgress(userId, courseId)
  const isCompleted = progress === 100

  if (!isCompleted) {
    return redirect(`/courses/${courseId}`)
  }

  // Get user's existing review
  const userReview = await db.review.findUnique({
    where: { userId_courseId: { userId, courseId } },
  })

  // Get completion date (from the last completed chapter)
  const lastCompletedChapter = await db.userProgress.findFirst({
    where: {
      userId,
      chapterId: { in: course.chapters.map(c => c.id) },
      isCompleted: true,
    },
    orderBy: { updatedAt: 'desc' },
  })

  const completionDate = lastCompletedChapter?.updatedAt

  // Get user information
  const userInfo = await getUserInfo(userId)

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-3 py-6">
        {/* Celebration Header */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-full mb-4">
            <Trophy className="h-10 w-10 text-primary" />
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-2">
            Selamat, {userInfo.firstName || 'Siswa'}! 🎉
          </h1>
          <p className="text-xl text-muted-foreground mb-4">
            Anda telah menyelesaikan kursus
          </p>
          <h2 className="text-2xl font-semibold text-primary mb-6">
            &ldquo;{course.title}&rdquo;
          </h2>

          <div className="flex items-center justify-center gap-4 mb-4">
            <Badge variant="secondary" className="px-4 py-2">
              <CheckCircle className="h-4 w-4 mr-2" />
              100% Selesai
            </Badge>
            {completionDate && (
              <Badge variant="outline" className="px-4 py-2">
                Diselesaikan: {completionDate.toLocaleDateString('id-ID')}
              </Badge>
            )}
          </div>

          <div className="flex justify-center gap-4">
            <Link href="/dashboard">
              <Button variant="outline">
                Kembali ke Dashboard
              </Button>
            </Link>
            <Link href="/search">
              <Button>
                Jelajahi Kursus Lain
              </Button>
            </Link>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Course Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Ringkasan Kursus
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">
                    {course.chapters.length}
                  </div>
                  <p className="text-sm text-muted-foreground">Bab Diselesaikan</p>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">
                    100%
                  </div>
                  <p className="text-sm text-muted-foreground">Progress</p>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">
                    {completionDate ?
                      Math.ceil((completionDate.getTime() - course.createdAt.getTime()) / (1000 * 60 * 60 * 24))
                      : '-'
                    }
                  </div>
                  <p className="text-sm text-muted-foreground">Hari untuk Menyelesaikan</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Certificate Section */}
          <CourseCertificate
            courseId={courseId}
            courseName={course.title}
            isCompleted={true}
            completionDate={completionDate}
          />

          {/* Reviews Section */}
          <CourseReviews
            courseId={courseId}
            canReview={true}
            userReview={userReview ? {
              ...userReview,
              createdAt: userReview.createdAt.toISOString()
            } : null}
          />

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle>Langkah Selanjutnya</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg bg-card">
                  <h3 className="font-semibold mb-2 text-foreground">📚 Jelajahi Kursus Lain</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Lanjutkan pembelajaran dengan kursus-kursus menarik lainnya
                  </p>
                  <Link href="/search">
                    <Button variant="outline" size="sm">
                      Lihat Kursus
                    </Button>
                  </Link>
                </div>

                <div className="p-4 border rounded-lg bg-card">
                  <h3 className="font-semibold mb-2 text-foreground">💼 Bagikan Pencapaian</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Bagikan sertifikat Anda di media sosial atau LinkedIn
                  </p>
                  <ShareButton courseTitle={course.title} />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
