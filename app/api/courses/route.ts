import { auth } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { isTeacher } from '@/lib/teacher'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()

    if (!userId || !isTeacher(userId)) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      createdById: userId,
    }

    if (search) {
      where.title = {
        contains: search,
        mode: 'insensitive'
      }
    }

    if (status !== 'all') {
      where.isPublished = status === 'published'
    }

    // Get total count for pagination
    const totalCourses = await db.course.count({ where })

    // Get courses with pagination
    const courses = await db.course.findMany({
      where,
      include: {
        category: true,
        chapters: {
          where: { isPublished: true }
        },
        purchases: true,
        _count: {
          select: {
            chapters: true,
            purchases: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    })

    const totalPages = Math.ceil(totalCourses / limit)

    return NextResponse.json({
      courses,
      pagination: {
        page,
        limit,
        totalCourses,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    })
  } catch (error) {
    console.log('[COURSES_GET]', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    const { title, categoryId, isFree } = await request.json()

    if (!userId || !isTeacher(userId)) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const course = await db.course.create({
      data: {
        title,
        categoryId,
        isFree: isFree || false,
        createdById: userId,
      },
    })

    return NextResponse.json(course)
  } catch (error) {
    console.log('[COURSES_POST]', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
