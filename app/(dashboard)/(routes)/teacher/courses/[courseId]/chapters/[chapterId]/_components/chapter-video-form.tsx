'use client'

import * as z from 'zod'
import axios from 'axios'
import MuxPlayer from '@mux/mux-player-react'
import { Pencil, PlusCircle, Video } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import { Chapter, MuxData } from '@prisma/client'
import { Button } from '@/components/ui/button'
import { FileUpload } from '@/components/file-upload'

interface ChapterVideoFormProps {
  initialData: Chapter & { muxData?: MuxData | null }
  courseId: string
  chapterId: string
}

const formSchema = z.object({
  videoUrl: z.string().min(1),
})

export const ChapterVideoForm = ({ initialData, courseId, chapterId }: ChapterVideoFormProps) => {
  const [isEditing, setIsEditing] = useState(false)

  const toggleEdit = () => setIsEditing((current) => !current)

  const router = useRouter()

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      formSchema.parse(values)
      await axios.patch(`/api/courses/${courseId}/chapters/${chapterId}`, values)
      toast.success('Bab diperbarui')
      toggleEdit()
      router.refresh()
    } catch {
      toast.error('Terjadi kesalahan')
    }
  }

  return (
    <div className="rounded-md border bg-muted/30 p-4">
      <div className="flex items-center justify-between font-medium">
        <span className="text-sm font-medium">Video Bab</span>
        <Button onClick={toggleEdit} variant="ghost" size="sm">
          {isEditing && <>Batal</>}
          {!isEditing && !initialData.videoUrl && (
            <>
              <PlusCircle className="mr-2 h-4 w-4" />
              Tambah Video
            </>
          )}
          {!isEditing && initialData.videoUrl && (
            <>
              <Pencil className="mr-2 h-4 w-4" />
              Edit Video
            </>
          )}
        </Button>
      </div>
      {!isEditing &&
        (!initialData.videoUrl ? (
          <div className="flex h-60 items-center justify-center rounded-md bg-muted border-2 border-dashed border-muted-foreground/25 mt-2">
            <Video className="h-10 w-10 text-muted-foreground" />
          </div>
        ) : (
          <div className="relative mt-2 aspect-video">
            <MuxPlayer
              playbackId={initialData?.muxData?.playbackId || ''}
              onError={(error) => {
                console.error('Mux player error:', error);
              }}
            />
          </div>
        ))}
      {isEditing && (
        <div className="mt-4">
          <FileUpload
            endpoint="chapterVideo"
            onChange={(url) => {
              if (url) {
                onSubmit({ videoUrl: url })
              }
            }}
          />
          <div className="mt-4 text-xs text-muted-foreground">
            Unggah video pembelajaran untuk bab ini
          </div>
        </div>
      )}
      {initialData.videoUrl && !isEditing && (
        <div className="mt-2 text-xs text-muted-foreground">
          Video mungkin memerlukan beberapa menit untuk diproses. Refresh halaman jika video tidak muncul.
        </div>
      )}
    </div>
  )
}
