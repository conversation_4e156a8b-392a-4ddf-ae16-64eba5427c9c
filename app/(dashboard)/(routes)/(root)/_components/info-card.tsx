import { LucideIcon } from 'lucide-react'

import { IconBadge } from '@/components/icon-badge'

interface InfoCardProps {
  numberOfItems: number
  variant?: 'default' | 'success'
  label: string
  icon: LucideIcon
  format?: 'default' | 'percentage'
}

export const InfoCard = ({ variant, icon: Icon, numberOfItems, label, format = 'default' }: InfoCardProps) => {
  const formatValue = () => {
    if (format === 'percentage') {
      return `${numberOfItems}%`
    }
    return numberOfItems.toString()
  }

  const formatLabel = () => {
    if (format === 'percentage') {
      return ''
    }
    return numberOfItems === 1 ? 'Kursus' : 'Kursus'
  }

  return (
    <div className="flex items-center gap-x-2 rounded-md border p-2">
      <IconBadge variant={variant} icon={Icon} />
      <div>
        <p className="font-medium">{label}</p>
        <p className="text-sm text-gray-500">
          {formatValue()} {formatLabel()}
        </p>
      </div>
    </div>
  )
}
