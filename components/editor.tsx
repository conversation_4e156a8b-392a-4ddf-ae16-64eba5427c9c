'use client'

import dynamic from 'next/dynamic'
import { useMemo } from 'react'
import { useTheme } from 'next-themes'

import 'react-quill-new/dist/quill.snow.css'

interface EditorProps {
  onChange: (value: string) => void
  value: string
}

export const Editor = ({ onChange, value }: EditorProps) => {
  const { theme } = useTheme()
  const ReactQuill = useMemo(() => dynamic(() => import('react-quill-new'), { ssr: false }), [])

  return (
    <div className={`quill-editor ${theme === 'dark' ? 'dark-mode' : ''}`}>
      <ReactQuill theme="snow" value={value} onChange={onChange} />
    </div>
  )
}
