import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { getEnhancedAnalytics } from '@/actions/get-enhanced-analytics'
import { EnhancedAnalyticsDashboard } from './_components/enhanced-analytics-dashboard'

export default async function Analytics() {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const analyticsData = await getEnhancedAnalytics(userId)

  return (
    <div className="p-3">
      <div className="mb-4">
        <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
        <p className="text-muted-foreground">
          Pantau performa kursus dan engagement siswa Anda
        </p>
      </div>

      <EnhancedAnalyticsDashboard data={analyticsData} />
    </div>
  )
}
