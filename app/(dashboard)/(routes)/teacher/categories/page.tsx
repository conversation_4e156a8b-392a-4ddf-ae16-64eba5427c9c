import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { db } from '@/lib/db'
import { DataTable } from './_components/data-table'
import { columns } from './_components/columns'
import { CategoryForm } from './_components/category-form'

export default async function CategoriesPage() {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const categories = await db.category.findMany({
    orderBy: { name: 'asc' },
    include: {
      courses: {
        select: { id: true }
      }
    }
  })

  return (
    <div className="space-y-4 p-3">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Manajemen Kategori</h1>
          <p className="text-sm text-muted-foreground">
            Ke<PERSON>la kategori kursus untuk mengorganisir konten Anda
          </p>
        </div>
      </div>
      
      <CategoryForm />
      
      <div className="border rounded-lg">
        <DataTable columns={columns} data={categories} />
      </div>
    </div>
  )
}
