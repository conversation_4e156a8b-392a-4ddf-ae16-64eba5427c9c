import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { db } from '@/lib/db'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, MessageSquare, User, Calendar, BookOpen } from 'lucide-react'
import Link from 'next/link'

export default async function TeacherReviewsPage() {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  // Get all reviews for teacher's courses
  const reviews = await db.review.findMany({
    where: {
      course: {
        createdById: userId
      }
    },
    include: {
      course: {
        select: {
          id: true,
          title: true,
          imageUrl: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })

  // Calculate review statistics
  const totalReviews = reviews.length
  const averageRating = totalReviews > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
    : 0

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: totalReviews > 0 ? (reviews.filter(review => review.rating === rating).length / totalReviews) * 100 : 0
  }))

  // Group reviews by course
  const reviewsByCourse = reviews.reduce((acc, review) => {
    const courseId = review.course.id
    if (!acc[courseId]) {
      acc[courseId] = {
        course: review.course,
        reviews: [],
        averageRating: 0,
        totalReviews: 0
      }
    }
    acc[courseId].reviews.push(review)
    return acc
  }, {} as Record<string, { course: any, reviews: any[], averageRating: number, totalReviews: number }>)

  // Calculate averages for each course
  Object.keys(reviewsByCourse).forEach(courseId => {
    const courseData = reviewsByCourse[courseId]
    courseData.totalReviews = courseData.reviews.length
    courseData.averageRating = courseData.reviews.reduce((sum, review) => sum + review.rating, 0) / courseData.totalReviews
  })

  return (
    <div className="space-y-4 p-3">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Review Kursus</h1>
          <p className="text-muted-foreground">
            Kelola dan pantau review dari siswa Anda
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="px-3 py-1">
            <MessageSquare className="h-4 w-4 mr-1" />
            {totalReviews} Review
          </Badge>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Review</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalReviews}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Rating Rata-rata</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-4 w-4 ${
                      star <= averageRating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Review Positif</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round((reviews.filter(r => r.rating >= 4).length / totalReviews) * 100) || 0}%
            </div>
            <p className="text-xs text-muted-foreground">Rating 4-5 bintang</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Kursus dengan Review</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(reviewsByCourse).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Rating Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Distribusi Rating</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {ratingDistribution.map(({ rating, count, percentage }) => (
              <div key={rating} className="flex items-center gap-4">
                <div className="flex items-center gap-1 w-16">
                  <span className="text-sm font-medium">{rating}</span>
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <div className="text-sm text-muted-foreground w-12 text-right">
                  {count}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Reviews by Course */}
      {Object.keys(reviewsByCourse).length > 0 ? (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold">Review per Kursus</h2>
          {Object.values(reviewsByCourse).map((courseData) => (
            <Card key={courseData.course.id}>
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                    {courseData.course.imageUrl ? (
                      <img
                        src={courseData.course.imageUrl}
                        alt={courseData.course.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <BookOpen className="h-8 w-8 text-blue-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="line-clamp-1">{courseData.course.title}</CardTitle>
                    <div className="flex items-center gap-4 mt-2">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-medium">{courseData.averageRating.toFixed(1)}</span>
                      </div>
                      <Badge variant="secondary">
                        {courseData.totalReviews} review
                      </Badge>
                    </div>
                  </div>
                  <Link href={`/teacher/courses/${courseData.course.id}`}>
                    <Badge variant="outline">Kelola Kursus</Badge>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {courseData.reviews.slice(0, 10).map((review) => (
                    <div key={review.id} className="border rounded-lg p-3">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="flex gap-1">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                  key={star}
                                  className={`h-3 w-3 ${
                                    star <= review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="text-sm text-muted-foreground">
                              {review.createdAt.toLocaleDateString('id-ID')}
                            </span>
                          </div>
                          {review.comment && (
                            <p className="text-sm text-gray-700">{review.comment}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {courseData.reviews.length > 10 && (
                    <div className="text-center">
                      <Badge variant="outline">
                        +{courseData.reviews.length - 10} review lainnya
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-300 mb-3" />
            <h3 className="text-xl font-semibold mb-2">Belum Ada Review</h3>
            <p className="text-gray-600 text-center mb-4">
              Review akan muncul setelah siswa menyelesaikan kursus Anda
            </p>
            <Link href="/teacher/courses">
              <Badge variant="outline">Kelola Kursus</Badge>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
