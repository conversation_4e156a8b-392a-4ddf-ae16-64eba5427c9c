'use client'

import { useState } from 'react'
import { Category } from '@prisma/client'
import {
  FcEngineering,
  FcFilmReel,
  FcMultipleDevices,
  FcMusic,
  FcOldTimeCamera,
  FcSalesPerformance,
  FcSportsMode,
  FcReading,
  FcCalculator,
  FcBusinessman,
  FcPicture,
  FcLike,
} from 'react-icons/fc'
import { IconType } from 'react-icons'
import { CategoryItem } from './category-item'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Grid, MoreHorizontal } from 'lucide-react'

interface CategoriesProps {
  items: Category[]
}

const iconMap: Record<Category['name'], IconType> = {
  'Ilmu Komputer': FcMultipleDevices,
  'Musik': FcMusic,
  'Kebugaran': FcSportsMode,
  'Fotografi': FcOldTimeCamera,
  'Akuntansi': FcSalesPerformance,
  'Teknik': FcEngineering,
  'Pembuatan Film': FcFilmReel,
  'Bahasa': FcReading,
  'Matematika': FcCalculator,
  'Bisnis': FcBusinessman,
  'Seni': FcPicture,
  'Memasak': FcLike,
}

export const Categories = ({ items }: CategoriesProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Show top 5 categories in the main view
  const topCategories = items.slice(0, 5)
  const hasMoreCategories = items.length > 5

  return (
    <div className="flex items-center gap-2 overflow-x-auto">
      {/* Top 5 Categories */}
      {topCategories.map((item) => (
        <CategoryItem key={item.id} label={item.name} icon={iconMap[item.name]} value={item.id} />
      ))}

      {/* Show All Button */}
      {hasMoreCategories && (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2 whitespace-nowrap border-dashed hover:border-solid"
            >
              <MoreHorizontal className="h-4 w-4" />
              Lihat Semua ({items.length - 5} lagi)
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Grid className="h-5 w-5" />
                Semua Kategori Kursus
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 mt-4">
              {items.map((item) => (
                <CategoryItem
                  key={item.id}
                  label={item.name}
                  icon={iconMap[item.name]}
                  value={item.id}
                  onClick={() => setIsModalOpen(false)}
                />
              ))}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
