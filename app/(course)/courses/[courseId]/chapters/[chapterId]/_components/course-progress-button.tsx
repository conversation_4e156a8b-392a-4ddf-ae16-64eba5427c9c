'use client'

import axios from 'axios'
import { CheckCircle, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import toast from 'react-hot-toast'

import { Button } from '@/components/ui/button'
import { useConfettiStore } from '@/hooks/use-confetti'

interface CourseProgressButtonProps {
  chapterId: string
  courseId: string
  isCompleted?: boolean
  nextChapterId?: string
}

export const CourseProgressButton = ({
  chapterId,
  courseId,
  isCompleted,
  nextChapterId,
}: CourseProgressButtonProps) => {
  const router = useRouter()
  const confetti = useConfettiStore()
  const [isLoading, setIsLoading] = useState(false)

  const onClick = async () => {
    try {
      setIsLoading(true)

      await axios.put(`/api/courses/${courseId}/chapters/${chapterId}/progress`, {
        isCompleted: !isCompleted,
      })

      if (!isCompleted && !nextChapterId) {
        // Course completed! Show confetti and redirect to completion page
        confetti.onOpen()
        toast.success('Selamat! Anda telah menyelesaikan kursus ini! 🎉')

        // Redirect to completion page after a short delay
        setTimeout(() => {
          router.push(`/courses/${courseId}/complete`)
        }, 2000)
      } else if (!isCompleted && nextChapterId) {
        router.push(`/courses/${courseId}/chapters/${nextChapterId}`)
        toast.success('Bab selesai! Lanjut ke bab berikutnya.')
      } else {
        toast.success('Progress diperbarui')
        router.refresh()
      }
    } catch {
      toast.error('Terjadi kesalahan')
    } finally {
      setIsLoading(false)
    }
  }

  const Icon = isCompleted ? XCircle : CheckCircle

  return (
    <Button
      onClick={onClick}
      disabled={isLoading}
      type="button"
      variant={isCompleted ? 'outline' : 'default'}
      className="w-full md:w-auto"
    >
      {isCompleted ? 'Tandai Belum Selesai' : 'Tandai Selesai'}
      <Icon className="ml-2 h-4 w-4" />
    </Button>
  )
}
