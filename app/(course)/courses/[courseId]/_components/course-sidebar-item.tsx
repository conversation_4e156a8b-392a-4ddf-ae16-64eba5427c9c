'use client'

import { CheckCircleIcon, LockIcon, PlayCircleIcon } from 'lucide-react'
import { usePathname, useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'

type CourseSidebarItemProps = {
  id: string
  label: string
  isCompleted: boolean
  courseId: string
  isLocked: boolean
}

export default function CourseSidebarItem({ id, label, isCompleted, courseId, isLocked }: CourseSidebarItemProps) {
  const pathname = usePathname()
  const router = useRouter()

  const Icon = isLocked ? LockIcon : isCompleted ? CheckCircleIcon : PlayCircleIcon
  const isActive = pathname?.includes(id)

  const onClick = () => {
    router.push(`/courses/${courseId}/chapters/${id}`)
  }

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        onClick={onClick}
        isActive={isActive}
        className={cn(
          'w-full justify-start text-sm font-medium transition-all',
          {
            'text-emerald-700 hover:text-emerald-700': isCompleted,
            'text-muted-foreground': isLocked,
          }
        )}
        disabled={isLocked}
      >
        <Icon
          size={16}
          className={cn(
            'shrink-0',
            {
              'text-emerald-700': isCompleted,
              'text-muted-foreground': isLocked,
            }
          )}
        />
        <span className="truncate">{label}</span>
        {isActive && (
          <div className={cn(
            'ml-auto h-2 w-2 rounded-full bg-sidebar-primary',
            { 'bg-emerald-700': isCompleted }
          )} />
        )}
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}
