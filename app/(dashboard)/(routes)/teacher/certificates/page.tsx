import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { db } from '@/lib/db'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Award, Calendar, BookOpen, Users, TrendingUp } from 'lucide-react'
import Link from 'next/link'

export default async function TeacherCertificatesPage() {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  // Get all certificates for teacher's courses
  const certificates = await db.certificate.findMany({
    where: {
      course: {
        createdById: userId
      }
    },
    include: {
      course: {
        select: {
          id: true,
          title: true,
          imageUrl: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })

  // Calculate certificate statistics
  const totalCertificates = certificates.length
  
  // Group certificates by course
  const certificatesByCourse = certificates.reduce((acc, certificate) => {
    const courseId = certificate.course.id
    if (!acc[courseId]) {
      acc[courseId] = {
        course: certificate.course,
        certificates: [],
        totalCertificates: 0
      }
    }
    acc[courseId].certificates.push(certificate)
    return acc
  }, {} as Record<string, { course: any, certificates: any[], totalCertificates: number }>)

  // Calculate totals for each course
  Object.keys(certificatesByCourse).forEach(courseId => {
    const courseData = certificatesByCourse[courseId]
    courseData.totalCertificates = courseData.certificates.length
  })

  // Get monthly certificate trends (last 6 months)
  const monthlyTrends = []
  for (let i = 5; i >= 0; i--) {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)

    const monthCertificates = certificates.filter(cert => 
      cert.createdAt >= monthStart && cert.createdAt <= monthEnd
    )

    monthlyTrends.push({
      month: date.toLocaleDateString('id-ID', { month: 'short', year: 'numeric' }),
      count: monthCertificates.length
    })
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sertifikat Siswa</h1>
          <p className="text-muted-foreground">
            Pantau sertifikat yang telah diterbitkan untuk siswa Anda
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="px-3 py-1">
            <Award className="h-4 w-4 mr-1" />
            {totalCertificates} Sertifikat
          </Badge>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Sertifikat</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCertificates}</div>
            <p className="text-xs text-muted-foreground">Sertifikat diterbitkan</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Kursus dengan Sertifikat</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(certificatesByCourse).length}</div>
            <p className="text-xs text-muted-foreground">Kursus yang diselesaikan</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Bulan Ini</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {certificates.filter(cert => {
                const thisMonth = new Date()
                const certMonth = new Date(cert.createdAt)
                return certMonth.getMonth() === thisMonth.getMonth() && 
                       certMonth.getFullYear() === thisMonth.getFullYear()
              }).length}
            </div>
            <p className="text-xs text-muted-foreground">Sertifikat baru</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata per Kursus</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(certificatesByCourse).length > 0 
                ? Math.round(totalCertificates / Object.keys(certificatesByCourse).length)
                : 0
              }
            </div>
            <p className="text-xs text-muted-foreground">Sertifikat per kursus</p>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Tren Sertifikat (6 Bulan Terakhir)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-6 gap-4">
            {monthlyTrends.map((trend, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl font-bold text-blue-600">{trend.count}</div>
                <p className="text-xs text-muted-foreground">{trend.month}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Certificates by Course */}
      {Object.keys(certificatesByCourse).length > 0 ? (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Sertifikat per Kursus</h2>
          {Object.values(certificatesByCourse)
            .sort((a, b) => b.totalCertificates - a.totalCertificates)
            .map((courseData) => (
            <Card key={courseData.course.id}>
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-lg flex items-center justify-center">
                    {courseData.course.imageUrl ? (
                      <img
                        src={courseData.course.imageUrl}
                        alt={courseData.course.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <BookOpen className="h-8 w-8 text-yellow-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="line-clamp-1">{courseData.course.title}</CardTitle>
                    <div className="flex items-center gap-4 mt-2">
                      <div className="flex items-center gap-1">
                        <Award className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium">{courseData.totalCertificates} sertifikat</span>
                      </div>
                      <Badge variant="secondary">
                        {courseData.certificates.length} siswa lulus
                      </Badge>
                    </div>
                  </div>
                  <Link href={`/teacher/courses/${courseData.course.id}`}>
                    <Badge variant="outline">Kelola Kursus</Badge>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <h4 className="font-medium">Sertifikat Terbaru:</h4>
                  {courseData.certificates.slice(0, 5).map((certificate) => (
                    <div key={certificate.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Award className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          Sertifikat #{certificate.id.slice(-8).toUpperCase()}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Diterbitkan: {certificate.createdAt.toLocaleDateString('id-ID')}
                        </p>
                      </div>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Aktif
                      </Badge>
                    </div>
                  ))}
                  {courseData.certificates.length > 5 && (
                    <div className="text-center">
                      <Badge variant="outline">
                        +{courseData.certificates.length - 5} sertifikat lainnya
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Award className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-semibold mb-2">Belum Ada Sertifikat</h3>
            <p className="text-gray-600 text-center mb-6">
              Sertifikat akan diterbitkan setelah siswa menyelesaikan kursus Anda
            </p>
            <Link href="/teacher/courses">
              <Badge variant="outline">Kelola Kursus</Badge>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
