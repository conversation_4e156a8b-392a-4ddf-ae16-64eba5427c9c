import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'

import { db } from '@/lib/db'
import { Categories } from './_component/category'
import { SearchInput } from '@/components/search-input'
import { getCourses } from '@/actions/get-courses'
import CoursesList from '@/components/course-list'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { BookOpen, Filter, Search } from 'lucide-react'

interface SearchPageProps {
  searchParams: Promise<{
    title: string
    categoryId: string
  }>
}

const SearchPage = async ({ searchParams }: SearchPageProps) => {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const categories = await db.category.findMany({
    orderBy: {
      name: 'asc',
    },
  })

  const courses = await getCourses({
    userId,
    ...(await searchParams),
    limit: 20,
  })

  const currentParams = await searchParams
  const hasActiveSearch = currentParams.title || currentParams.categoryId
  const selectedCategory = categories.find(cat => cat.id === currentParams.categoryId)

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Search className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Jelajahi Kursus</h1>
              <p className="text-muted-foreground">Temukan kursus yang tepat untuk mengembangkan skill Anda</p>
            </div>
          </div>

          {/* Search Bar - Desktop */}
          <div className="hidden md:block">
            <SearchInput />
          </div>
        </div>

        {/* Mobile Search */}
        <div className="block md:hidden mb-6">
          <SearchInput />
        </div>

        {/* Filters Section */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-foreground">Filter berdasarkan kategori</span>
            </div>
            <Categories items={categories} />
          </CardContent>
        </Card>

        {/* Results Section */}
        <div className="space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <BookOpen className="h-5 w-5 text-muted-foreground" />
              <div>
                <h2 className="text-xl font-semibold text-foreground">
                  {hasActiveSearch ? 'Hasil Pencarian' : 'Semua Kursus'}
                </h2>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm text-muted-foreground">
                    {courses.length} kursus ditemukan
                  </span>
                  {selectedCategory && (
                    <Badge variant="secondary" className="text-xs">
                      {selectedCategory.name}
                    </Badge>
                  )}
                  {currentParams.title && (
                    <Badge variant="outline" className="text-xs">
                      "{currentParams.title}"
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Course Grid */}
          <CoursesList items={courses} />

          {/* Pagination Info */}
          {courses.length === 20 && (
            <Card className="mt-6">
              <CardContent className="p-4 text-center">
                <p className="text-sm text-muted-foreground">
                  Menampilkan 20 kursus pertama. Gunakan pencarian atau filter kategori untuk hasil yang lebih spesifik.
                </p>
              </CardContent>
            </Card>
          )}

          {/* Empty State */}
          {courses.length === 0 && (
            <Card className="mt-6">
              <CardContent className="p-8 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="p-4 bg-muted rounded-full">
                    <BookOpen className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      Tidak ada kursus ditemukan
                    </h3>
                    <p className="text-muted-foreground">
                      Coba ubah kata kunci pencarian atau pilih kategori yang berbeda
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default SearchPage
