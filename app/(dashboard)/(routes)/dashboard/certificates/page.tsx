import { redirect } from 'next/navigation'
import { auth } from '@clerk/nextjs/server'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Award, Eye, Calendar } from 'lucide-react'
import Link from 'next/link'
import { getDashboardCourses } from '@/actions/get-dashboard-courses'
import { db } from '@/lib/db'

export default async function CertificatesPage() {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const { completedCourses } = await getDashboardCourses(userId)

  // Get certificates data
  const certificates = await db.certificate.findMany({
    where: { userId },
    include: {
      course: {
        include: {
          category: true
        }
      }
    },
    orderBy: { issuedAt: 'desc' }
  })

  return (
    <div className="space-y-4 p-3">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sertifikat Saya</h1>
          <p className="text-muted-foreground">
            Koleksi sertifikat dari kursus yang telah Anda selesaikan
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="px-3 py-1">
            <Award className="h-4 w-4 mr-1" />
            {completedCourses.length} Sertifikat
          </Badge>
        </div>
      </div>

      {/* Certificates Grid */}
      {completedCourses.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Award className="h-12 w-12 text-gray-300 mb-3" />
            <h3 className="text-xl font-semibold mb-2">Belum Ada Sertifikat</h3>
            <p className="text-gray-600 text-center mb-4">
              Selesaikan kursus untuk mendapatkan sertifikat pertama Anda
            </p>
            <Link href="/search">
              <Button>
                Jelajahi Kursus
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {completedCourses.slice(0, 10).map((course) => {
              const certificate = certificates.find(cert => cert.courseId === course.id)

              return (
                <Card key={course.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg line-clamp-2">{course.title}</CardTitle>
                        {course.category && (
                          <Badge variant="secondary" className="mt-2">
                            {course.category.name}
                          </Badge>
                        )}
                      </div>
                      <div className="w-12 h-12 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                        <Award className="h-6 w-6 text-yellow-600" />
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-3">
                    {/* Certificate Info */}
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Diselesaikan: {certificate?.issuedAt
                            ? certificate.issuedAt.toLocaleDateString('id-ID')
                            : 'Tanggal tidak tersedia'
                          }
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">
                          <Award className="h-3 w-3 mr-1" />
                          Sertifikat Tersedia
                        </Badge>
                      </div>
                    </div>

                    {/* Course Stats */}
                    <div className="bg-gray-50 rounded-lg p-2">
                      <div className="grid grid-cols-2 gap-3 text-center">
                        <div>
                          <div className="text-lg font-bold text-blue-600">{course.chapters.length}</div>
                          <p className="text-xs text-gray-600">Bab</p>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-green-600">100%</div>
                          <p className="text-xs text-gray-600">Selesai</p>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-1">
                      <Link href={`/courses/${course.id}/complete`} className="flex-1">
                        <Button size="sm" className="w-full">
                          <Eye className="h-4 w-4 mr-1" />
                          Lihat Sertifikat
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Show More Button */}
          {completedCourses.length > 10 && (
            <div className="text-center mt-4">
              <Badge variant="outline" className="px-4 py-2">
                +{completedCourses.length - 10} sertifikat lainnya
              </Badge>
            </div>
          )}
        </>
      )}

      {/* Summary Stats */}
      {completedCourses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Ringkasan Pencapaian</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{completedCourses.length}</div>
                <p className="text-sm text-gray-600">Total Sertifikat</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {new Set(completedCourses.map(course => course.categoryId)).size}
                </div>
                <p className="text-sm text-gray-600">Kategori Berbeda</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {completedCourses.reduce((total, course) => total + course.chapters.length, 0)}
                </div>
                <p className="text-sm text-gray-600">Total Bab Selesai</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">100%</div>
                <p className="text-sm text-gray-600">Tingkat Penyelesaian</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
