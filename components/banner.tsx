import { AlertTriangle, CheckCircleIcon } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const bannerVariants = cva('border text-center p-3 text-sm flex items-center w-full', {
  variants: {
    variant: {
      warning: 'bg-destructive/10 border-destructive/20 text-destructive',
      success: 'bg-primary/10 border-primary/20 text-primary',
    },
  },
  defaultVariants: {
    variant: 'warning',
  },
})

interface BannerProps extends VariantProps<typeof bannerVariants> {
  label: string
}

const iconMap = {
  warning: AlertTriangle,
  success: CheckCircleIcon,
}

export const Banner = ({ label, variant }: BannerProps) => {
  const Icon = iconMap[variant || 'warning']

  return (
    <div className={cn(bannerVariants({ variant }))}>
      <Icon className="mr-2 h-4 w-4" />
      {label}
    </div>
  )
}
