'use client'

import Image from 'next/image'
import Link from 'next/link'
import { BookOpenIcon, Award } from 'lucide-react'
import { formatPrice } from '@/lib/format'
import { IconBadge } from './icon-badge'
import { CourseProgress } from './course-progress'
import { Button } from './ui/button'
import { Badge } from './ui/badge'

type CourseCardProps = {
  id: string
  title: string
  imageUrl: string
  chaptersLength: number
  price: number
  isFree: boolean
  progress: number | null
  category: string
  showCertificateButton?: boolean
}

export default function CourseCard({
  id,
  title,
  imageUrl,
  chaptersLength,
  price,
  isFree,
  progress,
  category,
  showCertificateButton = false,
}: CourseCardProps) {
  return (
    <Link href={`/courses/${id}`}>
      <div className="group h-full overflow-hidden rounded-lg border bg-card p-3 transition-all hover:shadow-md hover:border-primary/20">
        <div className="relative aspect-video w-full overflow-hidden rounded-md">
          <Image
            fill
            className="object-cover"
            alt={title}
            src={imageUrl || '/placeholder-course.svg'}
            onError={(e) => {
              console.error('Course image failed to load:', imageUrl);
            }}
          />
        </div>

        <div className="flex flex-col pt-2">
          <div className="line-clamp-2 text-lg font-semibold transition group-hover:text-primary md:text-base">
            {title}
          </div>
          <p className="text-xs text-muted-foreground mt-1">{category}</p>
          <div className="my-2 flex items-center gap-x-1 text-sm md:text-xs">
            <div className="flex items-center gap-x-1 text-muted-foreground">
              <IconBadge size="sm" icon={BookOpenIcon} />
              <span>
                {chaptersLength} {chaptersLength === 1 ? 'Bab' : 'Bab'}
              </span>
            </div>
          </div>

          {progress !== null ? (
            <div className="space-y-1">
              <CourseProgress variant={progress === 100 ? 'success' : 'default'} size="sm" value={progress} />
              {showCertificateButton && progress === 100 && (
                <Link href={`/courses/${id}/complete`} onClick={(e) => e.stopPropagation()}>
                  <Button size="sm" variant="outline" className="w-full">
                    <Award className="h-4 w-4 mr-1" />
                    Lihat Sertifikat
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            <p className="text-md font-semibold text-foreground md:text-sm">
              {isFree ? (
                <Badge variant="secondary" className="bg-green-100 text-green-800">GRATIS</Badge>
              ) : (
                formatPrice(price)
              )}
            </p>
          )}
        </div>
      </div>
    </Link>
  )
}
