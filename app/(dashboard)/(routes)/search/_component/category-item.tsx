'use client'

import qs from 'query-string'
import { IconType } from 'react-icons'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'

import { cn } from '@/lib/utils'

interface CategoryItemProps {
  label: string
  value?: string
  icon?: IconType
  onClick?: () => void
}

export const CategoryItem = ({ label, value, icon: Icon, onClick: onClickProp }: CategoryItemProps) => {
  const pathname = usePathname()
  const router = useRouter()
  const searchParams = useSearchParams()

  const currentCategoryId = searchParams.get('categoryId')
  const currentTitle = searchParams.get('title')

  const isSelected = currentCategoryId === value

  const handleClick = () => {
    const url = qs.stringifyUrl(
      {
        url: pathname,
        query: {
          title: currentTitle,
          categoryId: isSelected ? null : value,
        },
      },
      { skipNull: true, skipEmptyString: true },
    )

    router.push(url)

    // Call the optional onClick prop (for closing modal)
    if (onClickProp) {
      onClickProp()
    }
  }

  return (
    <button
      onClick={handleClick}
      className={cn(
        'flex items-center gap-x-2 rounded-lg border border-border px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground whitespace-nowrap',
        isSelected && 'border-primary bg-primary text-primary-foreground shadow-sm',
      )}
      type="button"
    >
      {Icon && <Icon size={18} />}
      <span className="truncate">{label}</span>
    </button>
  )
}
