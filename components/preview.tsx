'use client'

import dynamic from 'next/dynamic'
import { useMemo } from 'react'
import { useTheme } from 'next-themes'

import 'react-quill-new/dist/quill.snow.css'

interface PreviewProps {
  value: string
}

export const Preview = ({ value }: PreviewProps) => {
  const { theme } = useTheme()
  const ReactQuill = useMemo(() => dynamic(() => import('react-quill-new'), { ssr: false }), [])

  return (
    <div className={`quill-preview ${theme === 'dark' ? 'dark-mode' : ''}`}>
      <ReactQuill theme="bubble" value={value} readOnly />
    </div>
  )
}
