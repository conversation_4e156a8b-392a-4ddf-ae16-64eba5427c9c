import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { BookOpen, Star, Users, TrendingUp } from 'lucide-react'
import Link from 'next/link'
import { db } from '@/lib/db'
import { formatPrice } from '@/lib/format'

interface Course {
  id: string
  title: string
  categoryId?: string | null
}

interface CourseRecommendationsProps {
  completedCourses: Course[]
  userId: string
}

async function getRecommendedCourses(completedCourses: Course[], userId: string) {
  try {
    // Get categories from completed courses
    const completedCategoryIds = completedCourses
      .map(course => course.categoryId)
      .filter(Boolean) as string[]

    // Get user's purchased course IDs
    const userPurchases = await db.purchase.findMany({
      where: { userId },
      select: { courseId: true }
    })
    const purchasedCourseIds = userPurchases.map(p => p.courseId)

    // Get all completed course IDs
    const completedCourseIds = completedCourses.map(course => course.id)

    // Exclude courses user already has access to
    const excludeCourseIds = [...purchasedCourseIds, ...completedCourseIds]

    // Get recommended courses from same categories
    const categoryRecommendations = await db.course.findMany({
      where: {
        isPublished: true,
        categoryId: { in: completedCategoryIds },
        id: { notIn: excludeCourseIds }
      },
      include: {
        category: true,
        purchases: true,
        reviews: true
      },
      take: 3
    })

    // Get popular courses if not enough category recommendations
    const popularCourses = await db.course.findMany({
      where: {
        isPublished: true,
        id: { notIn: [...excludeCourseIds, ...categoryRecommendations.map(c => c.id)] }
      },
      include: {
        category: true,
        purchases: true,
        reviews: true
      },
      orderBy: {
        purchases: {
          _count: 'desc'
        }
      },
      take: 3 - categoryRecommendations.length
    })

    const allRecommendations = [...categoryRecommendations, ...popularCourses]

    // Calculate average ratings and add metadata
    return allRecommendations.map(course => {
      const averageRating = course.reviews.length > 0
        ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
        : 0

      return {
        ...course,
        averageRating,
        enrollmentCount: course.purchases.length,
        reviewCount: course.reviews.length
      }
    })
  } catch (error) {
    console.error('Error getting recommended courses:', error)
    return []
  }
}

export async function CourseRecommendations({ completedCourses, userId }: CourseRecommendationsProps) {
  const recommendedCourses = await getRecommendedCourses(completedCourses, userId)

  if (recommendedCourses.length === 0) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Rekomendasi Kursus untuk Anda
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendedCourses.map((course) => (
            <div
              key={course.id}
              className="border rounded-lg p-3 hover:shadow-md transition-shadow"
            >
              {/* Course Image */}
              <div className="w-full h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg mb-2 flex items-center justify-center">
                {course.imageUrl ? (
                  <img
                    src={course.imageUrl}
                    alt={course.title}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <BookOpen className="h-12 w-12 text-blue-600" />
                )}
              </div>

              {/* Course Info */}
              <div className="space-y-1">
                <h3 className="font-semibold text-sm line-clamp-2">{course.title}</h3>
                
                {course.category && (
                  <Badge variant="secondary" className="text-xs">
                    {course.category.name}
                  </Badge>
                )}

                {/* Stats */}
                <div className="flex items-center gap-4 text-xs text-gray-600">
                  {course.averageRating > 0 && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span>{course.averageRating.toFixed(1)}</span>
                      <span>({course.reviewCount})</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{course.enrollmentCount} siswa</span>
                  </div>
                </div>

                {/* Price */}
                <div className="flex items-center justify-between">
                  <div className="font-semibold text-sm">
                    {course.isFree ? (
                      <Badge className="bg-green-100 text-green-800">Gratis</Badge>
                    ) : (
                      <span>{formatPrice(course.price || 0)}</span>
                    )}
                  </div>
                </div>

                {/* Action Button */}
                <Link href={`/courses/${course.id}`} className="block">
                  <Button size="sm" className="w-full mt-2">
                    Lihat Kursus
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* View More Button */}
        <div className="text-center mt-4">
          <Link href="/search">
            <Button variant="outline">
              <BookOpen className="h-4 w-4 mr-2" />
              Jelajahi Lebih Banyak Kursus
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
