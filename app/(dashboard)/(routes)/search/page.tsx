import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'

import { db } from '@/lib/db'
import { Categories } from './_component/category'
import { SearchInput } from '@/components/search-input'
import { getCourses } from '@/actions/get-courses'
import CoursesList from '@/components/course-list'

interface SearchPageProps {
  searchParams: Promise<{
    title: string
    categoryId: string
  }>
}

const SearchPage = async ({ searchParams }: SearchPageProps) => {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const categories = await db.category.findMany({
    orderBy: {
      name: 'asc',
    },
  })

  const courses = await getCourses({
    userId,
    ...(await searchParams),
    limit: 20,
  })

  return (
    <>
      <div className="block px-3 pt-4 md:mb-0 md:hidden">
        <SearchInput />
      </div>
      <div className="space-y-3 p-3">
        <Categories items={categories} />
        <CoursesList items={courses} />
        {courses.length === 20 && (
          <div className="text-center text-sm text-muted-foreground mt-4">
            Menampilkan 20 kursus pertama. Gunakan pencarian atau filter kategori untuk hasil yang lebih spesifik.
          </div>
        )}
      </div>
    </>
  )
}

export default SearchPage
