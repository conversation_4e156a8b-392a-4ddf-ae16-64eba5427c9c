'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Award, Calendar, TrendingUp } from 'lucide-react'
import { useEffect, useState } from 'react'
import { db } from '@/lib/db'

interface Certificate {
  id: string
  userId: string
  createdAt: string
  issuedAt: string
}

interface CourseCertificatesSectionProps {
  courseId: string
}

export function CourseCertificatesSection({ courseId }: CourseCertificatesSectionProps) {
  const [certificates, setCertificates] = useState<Certificate[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchCertificates = async () => {
      try {
        // In a real app, you'd create an API endpoint for this
        // For now, we'll simulate the data
        const mockCertificates: Certificate[] = []
        setCertificates(mockCertificates)
      } catch (error) {
        console.error('Error fetching certificates:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchCertificates()
  }, [courseId])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Sertifikat Diterbitkan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate monthly trends (last 6 months)
  const monthlyTrends = []
  for (let i = 5; i >= 0; i--) {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)

    const monthCertificates = certificates.filter(cert => {
      const certDate = new Date(cert.createdAt)
      return certDate >= monthStart && certDate <= monthEnd
    })

    monthlyTrends.push({
      month: date.toLocaleDateString('id-ID', { month: 'short' }),
      count: monthCertificates.length
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Sertifikat Diterbitkan
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {certificates.length > 0 ? (
          <>
            {/* Certificate Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{certificates.length}</div>
                <p className="text-sm text-muted-foreground">Total Sertifikat</p>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {certificates.filter(cert => {
                    const thisMonth = new Date()
                    const certMonth = new Date(cert.createdAt)
                    return certMonth.getMonth() === thisMonth.getMonth() && 
                           certMonth.getFullYear() === thisMonth.getFullYear()
                  }).length}
                </div>
                <p className="text-sm text-muted-foreground">Bulan Ini</p>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {certificates.filter(cert => {
                    const lastWeek = new Date()
                    lastWeek.setDate(lastWeek.getDate() - 7)
                    return new Date(cert.createdAt) >= lastWeek
                  }).length}
                </div>
                <p className="text-sm text-muted-foreground">7 Hari Terakhir</p>
              </div>
            </div>

            {/* Monthly Trends */}
            <div>
              <h4 className="font-semibold mb-4 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Tren 6 Bulan Terakhir
              </h4>
              <div className="grid grid-cols-6 gap-2">
                {monthlyTrends.map((trend, index) => (
                  <div key={index} className="text-center p-2 border rounded">
                    <div className="text-lg font-bold text-blue-600">{trend.count}</div>
                    <p className="text-xs text-muted-foreground">{trend.month}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Certificates */}
            <div>
              <h4 className="font-semibold mb-4">Sertifikat Terbaru</h4>
              <div className="space-y-3">
                {certificates.slice(0, 5).map((certificate) => (
                  <div key={certificate.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Award className="h-4 w-4 text-yellow-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        Sertifikat #{certificate.id.slice(-8).toUpperCase()}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Diterbitkan: {new Date(certificate.issuedAt).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Aktif
                    </Badge>
                  </div>
                ))}
                {certificates.length > 5 && (
                  <div className="text-center">
                    <Badge variant="outline">
                      +{certificates.length - 5} sertifikat lainnya
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <Award className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Belum Ada Sertifikat</h3>
            <p className="text-gray-600">
              Sertifikat akan diterbitkan setelah siswa menyelesaikan kursus ini
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
