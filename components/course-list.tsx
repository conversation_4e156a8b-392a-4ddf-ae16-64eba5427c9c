import { CourseWithProgressAndCategory } from '@/actions/get-courses'
import CourseCard from './course-card'

type CoursesListProps = {
  items: CourseWithProgressAndCategory[]
  showCertificateButton?: boolean
}

export default function CoursesList({ items, showCertificateButton = false }: CoursesListProps) {
  return (
    <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
      {items.map((course) => (
        <CourseCard
          key={course.id}
          id={course.id}
          title={course.title}
          imageUrl={course.imageUrl!}
          price={course.price!}
          isFree={course.isFree}
          progress={course.progress}
          chaptersLength={course.chapters.length}
          category={course?.category?.name ?? ''}
          showCertificateButton={showCertificateButton && course.progress === 100}
        />
      ))}
    </div>
  )
}
