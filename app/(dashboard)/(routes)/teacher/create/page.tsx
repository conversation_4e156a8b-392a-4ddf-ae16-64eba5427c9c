'use client'

import * as z from 'zod'
import axios from 'axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import { BookOpen, DollarSign, Tag, ArrowLeft, Loader2 } from 'lucide-react'

import { Form, FormControl, FormDescription, FormField, FormLabel, FormMessage, FormItem } from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'

const formSchema = z.object({
  title: z.string().min(3, {
    message: 'Judul kursus minimal 3 karakter',
  }).max(100, {
    message: 'Judul kursus maksimal 100 karakter'
  }),
  description: z.string().min(10, {
    message: 'Deskripsi minimal 10 karakter'
  }).optional(),
  categoryId: z.string().min(1, {
    message: 'Kategori harus dipilih'
  }),
  isFree: z.boolean().default(false),
  price: z.number().min(0).optional()
})

interface Category {
  id: string
  name: string
}

const CreatePage = () => {
  const router = useRouter()
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
      categoryId: '',
      isFree: true,
      price: 0
    },
  })

  const { isSubmitting, isValid } = form.formState
  const isFree = form.watch('isFree')

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get('/api/categories')
        setCategories(response.data)
      } catch (error) {
        console.error('Error fetching categories:', error)
        toast.error('Gagal memuat kategori')
      } finally {
        setIsLoadingCategories(false)
      }
    }

    fetchCategories()
  }, [])

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const submitData = {
        ...values,
        price: values.isFree ? 0 : values.price
      }

      const response = await axios.post('/api/courses', submitData)
      router.push(`/teacher/courses/${response.data.id}`)
      toast.success('Kursus berhasil dibuat!')
    } catch (error) {
      console.error('Error creating course:', error)
      toast.error('Gagal membuat kursus')
    }
  }

  return (
    <div className="container mx-auto py-4 px-3 max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <Link href="/teacher/courses">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Kursus
          </Button>
        </Link>
        <div className="space-y-1">
          <h1 className="text-3xl font-bold">Buat Kursus Baru</h1>
          <p className="text-muted-foreground">
            Isi informasi dasar untuk kursus Anda. Anda dapat mengubah semua informasi ini nanti.
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Left Column - Basic Information */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Informasi Dasar
                  </CardTitle>
                  <CardDescription>
                    Informasi utama tentang kursus Anda
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Judul Kursus *</FormLabel>
                        <FormControl>
                          <Input
                            disabled={isSubmitting}
                            placeholder="Contoh: Pemrograman Web untuk Pemula"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Judul yang menarik dan deskriptif akan membantu siswa menemukan kursus Anda
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Deskripsi Singkat</FormLabel>
                        <FormControl>
                          <Textarea
                            disabled={isSubmitting}
                            placeholder="Jelaskan secara singkat apa yang akan dipelajari siswa..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Deskripsi singkat tentang kursus (opsional, dapat diubah nanti)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Kategori *</FormLabel>
                        <Select
                          disabled={isSubmitting || isLoadingCategories}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih kategori kursus" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Pilih kategori yang paling sesuai dengan kursus Anda
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </div>
            {/* Right Column - Pricing & Settings */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Harga & Pengaturan
                  </CardTitle>
                  <CardDescription>
                    Tentukan model harga untuk kursus Anda
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <FormField
                    control={form.control}
                    name="isFree"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Kursus Gratis
                          </FormLabel>
                          <FormDescription>
                            Jadikan kursus ini gratis untuk semua siswa
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {!isFree && (
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Harga (IDR) *</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              disabled={isSubmitting}
                              placeholder="Contoh: 150000"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Harga dalam Rupiah (tanpa titik atau koma)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <div className="rounded-lg bg-muted p-3">
                    <h4 className="font-medium mb-2">💡 Tips Harga</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Kursus gratis bagus untuk membangun audiens</li>
                      <li>• Harga Rp 50.000 - 200.000 cocok untuk kursus pemula</li>
                      <li>• Harga Rp 200.000+ untuk kursus advanced</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Preview Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Tag className="h-5 w-5" />
                    Preview Kursus
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <h3 className="font-semibold">
                        {form.watch('title') || 'Judul Kursus'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {form.watch('description') || 'Deskripsi kursus akan muncul di sini...'}
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      {categories.find(cat => cat.id === form.watch('categoryId')) && (
                        <Badge variant="outline">
                          {categories.find(cat => cat.id === form.watch('categoryId'))?.name}
                        </Badge>
                      )}

                      <Badge variant="secondary" className="bg-primary/10 text-primary">
                        {isFree ? 'GRATIS' : `Rp ${(form.watch('price') || 0).toLocaleString('id-ID')}`}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <Link href="/teacher/courses">
              <Button type="button" variant="outline">
                Batal
              </Button>
            </Link>

            <Button
              type="submit"
              disabled={!isValid || isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Membuat...
                </>
              ) : (
                'Buat Kursus'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

export default CreatePage
