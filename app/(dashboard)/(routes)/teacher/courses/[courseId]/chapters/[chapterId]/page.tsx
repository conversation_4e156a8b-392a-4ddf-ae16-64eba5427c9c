import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Eye, LayoutDashboard, Video, CheckCircle, AlertCircle } from 'lucide-react'

import { db } from '@/lib/db'
import { IconBadge } from '@/components/icon-badge'
import { Banner } from '@/components/banner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

import { ChapterTitleForm } from './_components/chapter-title-form'
import { ChapterDescriptionForm } from './_components/chapter-description-form'
import { ChapterAccessForm } from './_components/chapter-access-form'
import { ChapterVideoForm } from './_components/chapter-video-form'
import { ChapterActions } from './_components/chapter-actions'

type ChapterIdPageProps = {
  params: Promise<{
    courseId: string
    chapterId: string
  }>
}

const ChapterIdPage = async ({ params }: ChapterIdPageProps) => {
  const resolvedParams = await params
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const chapter = await db.chapter.findUnique({
    where: {
      id: resolvedParams.chapterId,
      courseId: resolvedParams.courseId,
    },
    include: {
      muxData: true,
    },
  })

  if (!chapter) {
    return redirect('/')
  }

  const requiredFields = [chapter.title, chapter.description, chapter.videoUrl]

  const totalFields = requiredFields.length
  const completedFields = requiredFields.filter(Boolean).length

  const completionText = `(${completedFields}/${totalFields})`

  const isComplete = requiredFields.every(Boolean)

  return (
    <>
      {!chapter.isPublished && (
        <Banner variant="warning" label="Bab ini belum diterbitkan. Tidak akan terlihat dalam kursus" />
      )}
      <div className="container mx-auto py-4 px-3 max-w-7xl">
        {/* Header */}
        <div className="mb-4">
          <Link href={`/teacher/courses/${resolvedParams.courseId}`}>
            <Button variant="ghost" className="mb-2" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Kembali ke Pengaturan Kursus
            </Button>
          </Link>

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
            <div className="space-y-1">
              <h1 className="text-2xl font-bold">Pengaturan Bab</h1>
              <p className="text-sm text-muted-foreground">
                Kelola konten dan pengaturan bab kursus
              </p>
            </div>
            <ChapterActions
              disabled={!isComplete}
              courseId={resolvedParams.courseId}
              chapterId={resolvedParams.chapterId}
              isPublished={chapter.isPublished}
            />
          </div>
        </div>

        {/* Progress Card */}
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              {isComplete ? (
                <CheckCircle className="h-4 w-4 text-primary" />
              ) : (
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              )}
              Kelengkapan Bab
            </CardTitle>
            <CardDescription className="text-sm">
              Lengkapi semua field yang diperlukan untuk menerbitkan bab
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>Progress: {completedFields} dari {totalFields} field</span>
                <Badge variant={isComplete ? "default" : "secondary"} className="text-xs">
                  {isComplete ? "Siap Diterbitkan" : "Belum Lengkap"}
                </Badge>
              </div>
              <Progress value={(completedFields / totalFields) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Left Column - Chapter Content */}
          <div className="space-y-4">
            {/* Basic Information */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <IconBadge icon={LayoutDashboard} size="sm" />
                  Informasi Bab
                </CardTitle>
                <CardDescription className="text-sm">
                  Kustomisasi informasi dasar bab kursus
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-0">
                <ChapterTitleForm
                  initialData={chapter}
                  courseId={resolvedParams.courseId}
                  chapterId={resolvedParams.chapterId}
                />
                <ChapterDescriptionForm
                  initialData={chapter}
                  courseId={resolvedParams.courseId}
                  chapterId={resolvedParams.chapterId}
                />
              </CardContent>
            </Card>

            {/* Access Settings */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <IconBadge icon={Eye} size="sm" />
                  Pengaturan Akses
                </CardTitle>
                <CardDescription className="text-sm">
                  Tentukan siapa yang dapat mengakses bab ini
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ChapterAccessForm
                  initialData={chapter}
                  courseId={resolvedParams.courseId}
                  chapterId={resolvedParams.chapterId}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Video Content */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <IconBadge icon={Video} size="sm" />
                  Video Pembelajaran
                </CardTitle>
                <CardDescription className="text-sm">
                  Upload video utama untuk bab ini
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ChapterVideoForm
                  initialData={chapter}
                  chapterId={resolvedParams.chapterId}
                  courseId={resolvedParams.courseId}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
}

export default ChapterIdPage
