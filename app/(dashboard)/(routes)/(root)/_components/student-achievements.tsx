'use client'

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Trophy, Target, Flame, BookOpen, Star, Award } from 'lucide-react'

interface Course {
  id: string
  title: string
  progress: number
}

interface StudentAchievementsProps {
  completedCourses: Course[]
  coursesInProgress: Course[]
}

export function StudentAchievements({ completedCourses, coursesInProgress }: StudentAchievementsProps) {
  const totalCourses = completedCourses.length + coursesInProgress.length
  const completionRate = totalCourses > 0 ? (completedCourses.length / totalCourses) * 100 : 0

  // Calculate achievements
  const achievements = [
    {
      id: 'first-course',
      title: 'Kursus Pertama',
      description: 'Menyelesaikan kursus pertama',
      icon: BookOpen,
      earned: completedCourses.length >= 1,
      progress: Math.min(completedCourses.length, 1),
      target: 1,
      color: 'bg-primary/10 text-primary'
    },
    {
      id: 'course-collector',
      title: 'Pengumpul Kursus',
      description: 'Menyelesaikan 5 kursus',
      icon: Trophy,
      earned: completedCourses.length >= 5,
      progress: Math.min(completedCourses.length, 5),
      target: 5,
      color: 'bg-primary/10 text-primary'
    },
    {
      id: 'learning-master',
      title: 'Master Pembelajaran',
      description: 'Menyelesaikan 10 kursus',
      icon: Award,
      earned: completedCourses.length >= 10,
      progress: Math.min(completedCourses.length, 10),
      target: 10,
      color: 'bg-purple-100 text-purple-800'
    },
    {
      id: 'perfectionist',
      title: 'Perfeksionis',
      description: 'Tingkat penyelesaian 100%',
      icon: Target,
      earned: completionRate === 100 && totalCourses > 0,
      progress: Math.round(completionRate),
      target: 100,
      color: 'bg-green-100 text-green-800'
    },
    {
      id: 'learning-streak',
      title: 'Streak Belajar',
      description: 'Belajar secara konsisten',
      icon: Flame,
      earned: coursesInProgress.length > 0,
      progress: coursesInProgress.length > 0 ? 1 : 0,
      target: 1,
      color: 'bg-orange-100 text-orange-800'
    }
  ]

  const earnedAchievements = achievements.filter(achievement => achievement.earned)
  const nextAchievements = achievements.filter(achievement => !achievement.earned).slice(0, 2)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trophy className="h-5 w-5" />
          Pencapaian Anda
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Earned Achievements */}
        {earnedAchievements.length > 0 && (
          <div>
            <h3 className="font-semibold mb-3">Pencapaian yang Diraih 🎉</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {earnedAchievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-gradient-to-r from-yellow-50 to-yellow-100"
                >
                  <div className="w-10 h-10 bg-yellow-200 rounded-full flex items-center justify-center">
                    <achievement.icon className="h-5 w-5 text-yellow-700" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{achievement.title}</h4>
                    <p className="text-xs text-gray-600">{achievement.description}</p>
                  </div>
                  <Badge className={achievement.color}>
                    Selesai
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Next Achievements */}
        {nextAchievements.length > 0 && (
          <div>
            <h3 className="font-semibold mb-3">Pencapaian Selanjutnya 🎯</h3>
            <div className="space-y-3">
              {nextAchievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                    <achievement.icon className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{achievement.title}</h4>
                    <p className="text-xs text-muted-foreground mb-2">{achievement.description}</p>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(achievement.progress / achievement.target) * 100}%` }}
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {achievement.progress}/{achievement.target}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Learning Stats */}
        <div className="border-t pt-4">
          <h3 className="font-semibold mb-3">Statistik Pembelajaran 📊</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{completedCourses.length}</div>
              <p className="text-sm text-gray-600">Kursus Selesai</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{coursesInProgress.length}</div>
              <p className="text-sm text-gray-600">Sedang Belajar</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{completedCourses.length}</div>
              <p className="text-sm text-gray-600">Sertifikat</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{Math.round(completionRate)}%</div>
              <p className="text-sm text-gray-600">Tingkat Selesai</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
