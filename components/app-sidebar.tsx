"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import {
  <PERSON><PERSON><PERSON>,
  Compass,
  Layout,
  List,
  Tags,
  MessageSquare,
  Award,
  GraduationCap,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

// Guest routes for students
const guestRoutes = [
  {
    title: "Dasbor",
    url: "/",
    icon: Layout,
    isActive: false,
  },
  {
    title: "<PERSON><PERSON><PERSON><PERSON>",
    url: "/search",
    icon: Compass,
    isActive: false,
  },
  {
    title: "Sertifikat",
    url: "/dashboard/certificates",
    icon: Award,
    isActive: false,
  },
]

// Teacher routes for instructors
const teacherRoutes = [
  {
    title: "Kursus",
    url: "/teacher/courses",
    icon: List,
    isActive: false,
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    url: "/teacher/categories",
    icon: Tags,
    isActive: false,
  },
  {
    title: "Analitik",
    url: "/teacher/analytics",
    icon: Bar<PERSON><PERSON>,
    isActive: false,
  },
  {
    title: "Review",
    url: "/teacher/reviews",
    icon: MessageSquare,
    isActive: false,
  },
  {
    title: "Sertifikat",
    url: "/teacher/certificates",
    icon: Award,
    isActive: false,
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()

  // Determine if current page is teacher page
  const isTeacherPage = pathname?.startsWith('/teacher')

  // Get appropriate routes based on user role and current page
  const routes = isTeacherPage ? teacherRoutes : guestRoutes

  // Mark active route
  const navItems = routes.map(route => ({
    ...route,
    isActive: (pathname === '/' && route.url === '/') ||
              pathname === route.url ||
              pathname?.startsWith(`${route.url}/`)
  }))

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <GraduationCap className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold text-sm">LMS Platform</span>
                  <span className="truncate text-xs text-sidebar-foreground/70">Learning Management</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navItems} />
        <NavSecondary
          items={[]}
          className="mt-auto"
        />
      </SidebarContent>
    </Sidebar>
  )
}
