@tailwind base;
@tailwind components;
@tailwind utilities;

html, 
body, 
:root {
  height: 100%;
}
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 0 0% 45.1%;
    --sidebar-primary: 0 0% 9%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 0 0% 96.1%;
    --sidebar-accent-foreground: 0 0% 9%;
    --sidebar-border: 0 0% 89.8%;
    --sidebar-ring: 0 0% 3.9%;
  }
 
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 0 0% 3.9%;
    --sidebar-foreground: 0 0% 63.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 0 0% 9%;
    --sidebar-accent: 0 0% 14.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 14.9%;
    --sidebar-ring: 0 0% 83.1%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Quill Editor Base Styles */
.ql-container {
  font-size: 16px !important;
}

/* Light Mode Quill Styles (Default) */
.quill-editor .ql-editor,
.quill-preview .ql-editor {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 2px);
}

.quill-editor .ql-toolbar,
.quill-preview .ql-toolbar {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-bottom: none;
  border-radius: calc(var(--radius) - 2px) calc(var(--radius) - 2px) 0 0;
}

.quill-editor .ql-container,
.quill-preview .ql-container {
  border-radius: 0 0 calc(var(--radius) - 2px) calc(var(--radius) - 2px);
}

/* Dark Mode Quill Styles */
.dark .quill-editor.dark-mode .ql-editor,
.dark .quill-preview.dark-mode .ql-editor {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
}

.dark .quill-editor.dark-mode .ql-toolbar,
.dark .quill-preview.dark-mode .ql-toolbar {
  background-color: hsl(var(--muted));
  border-color: hsl(var(--border));
}

/* Toolbar Button Styles */
.dark .quill-editor.dark-mode .ql-toolbar .ql-stroke {
  stroke: hsl(var(--foreground));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-fill {
  fill: hsl(var(--foreground));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-picker-label {
  color: hsl(var(--foreground));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-picker-options {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-picker-item {
  color: hsl(var(--foreground));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-picker-item:hover {
  background-color: hsl(var(--muted));
}

/* Active/Selected States */
.dark .quill-editor.dark-mode .ql-toolbar button:hover,
.dark .quill-editor.dark-mode .ql-toolbar button.ql-active {
  background-color: hsl(var(--muted));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-stroke.ql-active {
  stroke: hsl(var(--primary));
}

.dark .quill-editor.dark-mode .ql-toolbar .ql-fill.ql-active {
  fill: hsl(var(--primary));
}

/* Placeholder Text */
.dark .quill-editor.dark-mode .ql-editor.ql-blank::before {
  color: hsl(var(--muted-foreground));
}

/* Selection Styles */
.dark .quill-editor.dark-mode .ql-editor ::selection {
  background-color: hsl(var(--primary) / 0.3);
}

/* Tooltip Styles */
.dark .quill-editor.dark-mode .ql-tooltip {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

.dark .quill-editor.dark-mode .ql-tooltip input {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

/* Preview Specific Styles */
.dark .quill-preview.dark-mode .ql-editor {
  background-color: transparent;
  border: none;
  padding: 0;
}

/* Ensure text content is visible in dark mode */
.dark .quill-editor.dark-mode .ql-editor p,
.dark .quill-editor.dark-mode .ql-editor h1,
.dark .quill-editor.dark-mode .ql-editor h2,
.dark .quill-editor.dark-mode .ql-editor h3,
.dark .quill-editor.dark-mode .ql-editor h4,
.dark .quill-editor.dark-mode .ql-editor h5,
.dark .quill-editor.dark-mode .ql-editor h6,
.dark .quill-editor.dark-mode .ql-editor ul,
.dark .quill-editor.dark-mode .ql-editor ol,
.dark .quill-editor.dark-mode .ql-editor li,
.dark .quill-editor.dark-mode .ql-editor blockquote,
.dark .quill-editor.dark-mode .ql-editor strong,
.dark .quill-editor.dark-mode .ql-editor em,
.dark .quill-preview.dark-mode .ql-editor p,
.dark .quill-preview.dark-mode .ql-editor h1,
.dark .quill-preview.dark-mode .ql-editor h2,
.dark .quill-preview.dark-mode .ql-editor h3,
.dark .quill-preview.dark-mode .ql-editor h4,
.dark .quill-preview.dark-mode .ql-editor h5,
.dark .quill-preview.dark-mode .ql-editor h6,
.dark .quill-preview.dark-mode .ql-editor ul,
.dark .quill-preview.dark-mode .ql-editor ol,
.dark .quill-preview.dark-mode .ql-editor li,
.dark .quill-preview.dark-mode .ql-editor blockquote,
.dark .quill-preview.dark-mode .ql-editor strong,
.dark .quill-preview.dark-mode .ql-editor em {
  color: hsl(var(--foreground)) !important;
}

/* Links in dark mode */
.dark .quill-editor.dark-mode .ql-editor a,
.dark .quill-preview.dark-mode .ql-editor a {
  color: hsl(var(--primary)) !important;
}

/* Code blocks in dark mode */
.dark .quill-editor.dark-mode .ql-editor code,
.dark .quill-editor.dark-mode .ql-editor pre,
.dark .quill-preview.dark-mode .ql-editor code,
.dark .quill-preview.dark-mode .ql-editor pre {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
}

@import "~@uploadthing/react/styles.css"